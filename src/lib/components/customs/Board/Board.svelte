<script lang="ts">
	import type { <PERSON><PERSON><PERSON><PERSON>, IWorkout } from "$lib/typings";
	import { onMount } from "svelte";
	import { dndzone, type DndEvent, SOURCES, TRIGGERS } from "svelte-dnd-action";
	import { flip } from "svelte/animate";
	import { writable, type Writable } from "svelte/store";
	import Workout from "./Workout.svelte";
    import { ScrollArea } from "$lib/components/ui/scroll-area/index.js";
    import { isBoardScrolled } from "$lib/stores/generalStore";

    interface IProgram {
        id: string,
        name: string,
        automated_name: string,
        workouts?: any[]
    }
    import { longpress } from "$lib/utils.js";


    let { 
        program, workoutsStore, programId, isDeleteMode, programName, programAutoName 
    } = $props<{ 
        program: IProgram, workoutsStore: Writable<IWorkout[]>, 
        programId: number, isDeleteMode: boolean, programName: string, programAutoName: string 
    }>();

    let items: IWorkout[] = $state([]);
    let scrollContainer: HTMLElement | null = $state(null);
    let autoScrollInterval: number | null = $state(null);
    let isMobile: boolean = $state(false);
    let isTablet: boolean = $state(false);
    let isTransitionRange: boolean = $state(false);
    let isScrolledHorizontally: boolean = $state(false);
    const SCROLL_SPEED = 40;
    const EDGE_THRESHOLD = 300;



    items = $workoutsStore;

    const board = writable({
        disabled: false,
    });

    let isDragging: boolean = $state(false);

    $effect(() => {
        if ($workoutsStore) {
            (async () => {
                items = $workoutsStore;
            })();
        }
        if (isMobile) {
            isDragging = true;
        } else {
            isDragging = false;
        }
    });

    const flipDurationMs = 150;

    let isWideLayout = $state(false);


    
//   defaultProgram.subscribe((info) => {
//         if (info) {
//             selectedProgram = info;
//             return;
//         }

//         selectedProgram = { 
//             value: programs[0].id, 
//             label: programs[0].name ? programs[0].name : programs[0].automated_name 
//         } 
//     })

    function handleDragStart() {
        isDragging = true;
    }

    function handleDragEnd() {
        isDragging = false;
        if (autoScrollInterval) {
            clearInterval(autoScrollInterval);
            autoScrollInterval = null;
        }
    }

    function handleDragOver(e: MouseEvent) {
        if (!isDragging || !scrollContainer) return;

        const rect = scrollContainer.getBoundingClientRect();
        const screenWidth = window.outerWidth;
        const isMobile = screenWidth <= 575;
        const isTablet = screenWidth > 575 && screenWidth < 1024;

        if (isMobile || isTablet) {
            // Vertical scrolling on mobile
            const topEdge = e.clientY - rect.top;
            const bottomEdge = rect.bottom - e.clientY;

            // if (topEdge < EDGE_THRESHOLD) {
            //     startAutoScroll('up');
            // } else if (bottomEdge < EDGE_THRESHOLD) {
            //     startAutoScroll('down');
            // } else {
            //     stopAutoScroll();
            // }
        } else {
            // Horizontal scrolling on desktop
            const leftEdge = e.clientX - rect.left;
            const rightEdge = rect.right - e.clientX;

            if (leftEdge < EDGE_THRESHOLD) {
                startAutoScroll('left');
            } else if (rightEdge < EDGE_THRESHOLD) {
                startAutoScroll('right');
            } else {
                stopAutoScroll();
            }
        }
    }

    function startAutoScroll(direction: 'left' | 'right' | 'up' | 'down') {
        if (autoScrollInterval) return;

        autoScrollInterval = setInterval(() => {
            if (!scrollContainer) return;

            switch (direction) {
                case 'left':
                    scrollContainer.scrollLeft -= SCROLL_SPEED;
                    break;
                case 'right':
                    scrollContainer.scrollLeft += SCROLL_SPEED;
                    break;
                case 'up':
                    scrollContainer.scrollTop -= SCROLL_SPEED;
                    break;
                case 'down':
                    scrollContainer.scrollTop += SCROLL_SPEED;
                    break;
            }
        }, 16) as unknown as number;
    }

    function stopAutoScroll() {
        if (autoScrollInterval) {
            clearInterval(autoScrollInterval);
            autoScrollInterval = null;
        }
    }

    async function handleBoardUpdated(newColumnsData: any[]) {
        const alphabet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'.split('');
        let j = 0;

        const updatedColumns = newColumnsData.map((item) => {
            return {
                ...item,
                automated_name: `Workout ${alphabet[j]}`,
                position: alphabet[j++]
            }
        });

        workoutsStore.set(updatedColumns);
        items = updatedColumns;

        const workouts = updatedColumns.map(item => ({
            id: item.id,
            automated_name: item.automated_name,
            position: item.position
        }));

        const res = await fetch(`/api/workouts`, { 
            method: 'PATCH', 
            headers: {
                'Content-Type': 'Application/json'
            }, 
            body: JSON.stringify({ items: workouts })
        });
        if (res.ok) {
            // workoutsStore.set(updatedColumns);
            // items = updatedColumns;
        }
    }

    function handleDndConsider(e: CustomEvent<DndEvent<IWorkout>>) {
        const {info: {source, trigger}} = e.detail;
        if (isDeleteMode) return;
        // // Ensure dragging is stopped on drag finish via keyboard
		// if (source === SOURCES.KEYBOARD && trigger === TRIGGERS.DRAG_STOPPED) {
		// 	isDragging = true;
		// }
        // handleDragStart();
        items = e.detail.items;
    }

    function handleDndFinalize(e: CustomEvent<DndEvent<IWorkout>>) {
        if (isDeleteMode) return;
        const {info: {source}} = e.detail;
		
        isMobile ? isDragging = true : isDragging = false;
        // handleDragEnd();
        // workoutsStore.set(e.detail.items);
        items = e.detail.items;
        handleBoardUpdated(e.detail.items);
        // const theDefaultProgramm = get(defaultProgram)
        // defaultProgram.set(theDefaultProgramm)
        // console.log('default.program in workout', theDefaultProgramm)
    }

    onMount(() => {
        console.log('program in board', program);
        isMobile = window.outerWidth <= 768;
        isTablet = window.outerWidth > 768 && window.outerWidth < 1033;
        const handleResize = () => {
            isTransitionRange = window.outerWidth >= 1033 && window.outerWidth < 1333;
            if (scrollContainer) {
                isWideLayout = scrollContainer.clientWidth >= 856;
            }
        };
        items = $workoutsStore;

        // Initial check
        handleResize();

        // Add resize event listener
        window.addEventListener('resize', handleResize);

        // Cleanup event listener on component destroy
        return () => {
            stopAutoScroll();
            window.removeEventListener('resize', handleResize);
        };
    });

    function startDrag(e: any) {
		isDragging = false;
	}
    const handleScroll = (event: Event) => {
    if (!scrollContainer) return;
    // Check if the scroll position both horizontal and vertical is greater than 10 pixels
    const isScrolled = scrollContainer.scrollTop > 10 || scrollContainer.scrollLeft > 10;
    isBoardScrolled.set(isScrolled); // Update the store

  };

  $effect(() => {
    if (scrollContainer) {
      scrollContainer.addEventListener('scroll', handleScroll);
      isWideLayout = scrollContainer.clientWidth >= 856;
      return () => {
        scrollContainer?.removeEventListener('scroll', handleScroll);
      };
    }
  });

  function handleAddWorkoutClick() {
    console.log("Add Workout button clicked!");
    // Add your logic for adding a workout here
  }
</script>


<section 
    bind:this={scrollContainer}
    id="scrollbox"
    role="presentation"
    class="p-1 flex flex-row {isTransitionRange ? 'gap-[454px]' : 'gap-4'} w-full h-[calc(100vh-100px)] overflow-auto"
    use:dndzone={{items: items, flipDurationMs, type: 'workout', dragDisabled: $board.disabled || isDragging}} 
    onconsider={handleDndConsider} 
    onfinalize={handleDndFinalize}
    onmousemove={handleDragOver}
>
    {#each items as {id, name, automated_name, exercises, position}, idx (`workout-${id}`)}
        <div animate:flip={{duration:flipDurationMs}} class="w-full {!isMobile || !isTablet ? 'lg:w-[428px]' : 'w-full'} shrink-0">
            <Workout 
                id={id}
                programId={programId}
                programName={programName}
                programAutoName={programAutoName}
                name={name}
                automated_name={automated_name}
                boardStore={board}
                {position}
                items={exercises}
                idx={idx}
                {isDeleteMode}
                {program}
                
                workoutsStore={workoutsStore}
            />
        </div>
    {/each}
</section>
    
