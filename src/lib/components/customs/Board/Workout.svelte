<script lang="ts">
	import { But<PERSON>, buttonVariants } from "$lib/components/ui/button";
    import * as AlertDialog from "$lib/components/ui/alert-dialog";
    import * as DropdownMenu from "$lib/components/ui/dropdown-menu";
	import { Input } from "$lib/components/ui/input";
	import type { IAddExerciseToWorkout, IExercise, IWorkout } from "$lib/typings";
	import { onMount } from "svelte";
	import { ChevronDown, ChevronUp, XLg, ThreeDotsVertical, Arrow90degLeft, Arrow90degRight, ArrowReturnRight, Trash, Arrow90degDown, Arrow90degUp, Copy } from "svelte-bootstrap-icons";
	import { dndzone, TRIGGERS, type DndEvent } from "svelte-dnd-action";
	import { toast } from "svelte-sonner";
	import { flip } from "svelte/animate";
	import Exercise from "./Exercise.svelte";
	import { writable, type Writable } from "svelte/store";
	import { addExerciseToWorkout, update<PERSON><PERSON>ban, deleteExerciseWorkout } from "$lib/utils/workouts_exercises.utils";
    import { ScrollArea } from "$lib/components/ui/scroll-area/index.js";
	import { workouts } from "../Kanban/data";
	import { get } from "svelte/store";
  import { longpressWorkoutItem } from "$lib/utils";
    
    import { defaultProgram } from "$lib/stores/generalStore";

    import { selectedWorkout, selectedProgram } from "$lib/stores/generalStore";
  import * as Dialog from "$lib/components/ui/dialog";

    interface IProgram {
        id: string,
        name: string,
        automated_name: string,
        workouts?: any[],
        store?: Writable<any>
    }

    let { 
        id, programId, name, automated_name, items, idx, 
        boardStore, isDeleteMode, programAutoName, programName, program, workoutsStore, position 
    } = $props<{ 
        id: string, programId: string, name: string, automated_name: string, items: IExercise[], 
        idx: number, boardStore: Writable<any>, isDeleteMode: boolean, programAutoName: string,
             programName: string, program: IProgram, position: string, workoutsStore: Writable<IWorkout[]>
    }>();


    let exercises: IExercise[] = $state([]);
    let newItems: any[] = $state([]);

    let isEditing: boolean = $state(false);
    let updatedName: string = $state(name || automated_name);
    let isMobileScreen: boolean = $state(false);
    let isTabletScreen: boolean = $state(false);
    let isWorkoutOpen: boolean = $state(idx === 0);
    let openDeleteWorkoutDialog: boolean = $state(false);
    let openDeleteExerciseDialog: boolean = $state(false);
    let contentWrapper: HTMLElement;


    let isDragging: boolean = $state(false);
    $effect(() => {
        if (isMobileScreen) {
            isDragging = true;
        } else {
            isDragging = false;
        }
    });


    let selectedExercise: { id: number | null,  name: string | null, exercise_id?: string | null } = $state({id: null, name: null});

    const draggedNode = writable<any> (null)
    const timer = writable<any>(null);

    let isExist = $state(false);


    const flipDurationMs = 150;

    async function editWorkoutName(event: KeyboardEvent) {
        if (updatedName.trim() === (name || automated_name)) {
            return;
        }

        if (event.key === 'Enter') {
            const res = await fetch('/api/workouts/' + id, {
                method: 'PATCH',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ name: updatedName.trim() })
            });

            const data = await res.json();

            if (!data.success) {
                toast.error(data.message);
            }
            
            name = updatedName;
            $workoutsStore[idx].name = updatedName;

            toast.success('Workout name updated successfully');

            isEditing = false
        } else if (event.key === 'Escape') {
            isEditing = false;
            updatedName = name || automated_name;
        }
    }

    function formatProgramName(startMonth: string, startYear: number, endMonth: string, endYear: number, alphabet: string) {
        if (startYear === endYear) {
            return startMonth === endMonth
                ? `${startMonth} ${startYear}${alphabet ? ' ' + alphabet : ''}`
                : `${startMonth} ${endMonth} ${startYear}${alphabet ? ' ' + alphabet : ''}`;
        } else {
            return `${startMonth} ${startYear} / ${endMonth} ${endYear}${alphabet ? ' ' + alphabet : ''}`;
        }
    }

    function capitalize(word: string) {
        return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
    }

    function renameProgram() {
        const currentDate = new Date();
        const endMonth = capitalize(currentDate.toLocaleString('en-us', { month: 'short' }));
        const endYear = currentDate.getFullYear();

        const name = program.automated_name.trim();
        const parts = name.split('/').map((part: string) => part.trim());

        let startMonth = '';
        let startYear = 0;
        let alphabet = '';

        const startWords = parts[0].split(/\s+/);
        for (let i = 0; i < startWords.length; i++) {
            const word = startWords[i];
            if (/^\d{4}$/.test(word)) {
                startYear = parseInt(word);
            } else if (/^[a-z]{3,9}$/i.test(word)) {
                if (!startMonth) startMonth = capitalize(word);
            }
        }

        const lastWord = parts[parts.length - 1].split(/\s+/).pop()?.toLowerCase() || '';
        const isYear = /^\d{4}$/.test(lastWord);
        const isMonth = /^[a-z]{3,9}$/i.test(lastWord);
        if (!isYear && !isMonth) {
            alphabet = lastWord.toUpperCase();
        }

        const newProgramName = formatProgramName(startMonth, startYear, endMonth, endYear, alphabet);

        fetch('/api/programs/' + program.id, {
            method: 'PATCH',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                automated_name: newProgramName
            })
        });
    }


    function handleDndConsider(e: CustomEvent<DndEvent<IExercise>>) {     
            exercises = e.detail.items;
    }
    
    async function handleDndFinalize({ detail: { info, items}, target }: CustomEvent<DndEvent<IExercise>>) {   
        exercises = items;

        const ex = items.find(obj => obj.id.toString() === info.id);



        if (info.trigger === TRIGGERS.DROPPED_INTO_ZONE && typeof info.id === 'string' && !ex?.data_id) {
            
            

            const temp = exercises.map((item: any, index: number) => {
                if (item.id === info.id) {
                    
                    if (item.isPending) return;

                      
                    exercises[index].isPending = true;
                    items[index].isPending = true;


                    return {
                
                        exercise: {
                            ...item,
                            id: item.exercise_id,
                            workout_exercise_id: undefined,
                        },
                        workout_id: id,
                        position: index + 1
                    }
                }

            });

            const newExercise = temp.filter(Boolean)[0];


           if (!newExercise) return;
           
            const res = await fetch('/api/add_exercise', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json'},
                body: JSON.stringify({item: newExercise}),
            });

            const data = await res.json();

            
            let exs: any[] = [];

             if(data?.success){
                exs = exercises.map((exercise: any) => {
                if(exercise.exercise_id.toString() === data.data.exercise_id && exercise.id === info.id ){
                    exercise.data_id = data.data.id;
                    exercise.isPending = false;
                }
                return exercise;
            });


            exercises = exs;


            workoutsStore.update((currentWorkouts: IWorkout[]) => {
                currentWorkouts[idx].exercises = exs;
                return currentWorkouts;
            })


            // const newItems: IAddExerciseToWorkout[] = exs.map((item: any, index: number)  => {

            //     if(item.isPending || !item.data_id) return;

            //     item.exercise_id = item.exercise_id.toString();
                
            //     item.exercise_id =  item.exercise_id.split("_")[0];
            //     item.color = item?.color;


            //     return {
                    
            //         exercise: {
            //             ...item,
            //             id: typeof item.data_id && !item.isPending ? undefined : item.data_id,
            //         },
            //         workout_id: id,
            //         position: index + 1
            //     }
            // })

            //  await updateKanban(newItems);

        }

       }
       else {



        const currentWorkout = {
            value: id,
            label: name || automated_name,
            // programName,
            // programAutoName
        };
        
        selectedWorkout.set(currentWorkout)
        const theDefaultProgram = get(defaultProgram)


        // exercises = items;

        workoutsStore.update((currentWorkouts: IWorkout[]) => {
            currentWorkouts[idx].exercises = items;
            return currentWorkouts;
        })


        isMobileScreen ? isDragging = true : isDragging = false;

        const newItems: IAddExerciseToWorkout[] = items.map((item: any, index: number)  => {

            item.exercise_id = item.exercise_id.toString();
            
            item.exercise_id =  item.exercise_id.split("_")[0];
            item.color = item?.color;

            return {
                
                exercise: item,
                workout_id: id,
                position: index + 1
            }
        })

        
        const data = await updateKanban(newItems);

    }

    }

    async function handleDeleteExercise() {     
        
        if (!selectedExercise.id)  {
            openDeleteExerciseDialog = false;
            toast.info('The exercise is still being saved. Please wait for the exercise to be saved before deleting it.');
            return;
        }

        selectedProgram.set({value: programId, label: program.name || program.automated_name});
        exercises = exercises.filter(exercise => exercise.data_id !== selectedExercise.id);

        console.log('exercises', exercises);   

        workoutsStore.update((currentWorkouts: IWorkout[]) => {
            currentWorkouts[idx].exercises = exercises;
            return currentWorkouts;
        })

        const res = await deleteExerciseWorkout(selectedExercise.id);
        if (!res) {
            toast.error("An error occurred while deleting the exercise from the workout");
            return;
        }

        toast.success(`Successfully deleted ${selectedExercise.name} from workout!`);
        openDeleteExerciseDialog = false;
    }


    async function handleDelete() {
        const res = await fetch('/api/workouts/' + id, {
            method: 'DELETE'
        });
        const data = await res.json();
        if(data?.success){

            const currentWorkouts = $workoutsStore;
            const updatedWorkouts = currentWorkouts.filter((workout: IWorkout) => workout.id !== id);
            const temp: any = []

            updatedWorkouts.forEach((w: IWorkout, i: number) => {
                const code = String.fromCharCode(65+i)
                temp.push({
                    ...updatedWorkouts[i],
                    position: code,
                    automated_name:  `Workout ${code}`
                })
            
            })

            workoutsStore.set(temp);


            await fetch(`/api/workouts`, { 
                method: 'PATCH', 
                headers: {
                    'Content-Type': 'Application/json'
                }, 
                body: JSON.stringify({ items: temp })
            });

            openDeleteWorkoutDialog = false;
            toast.success(data.message);

        } else {
            toast.error(data.message);
        }
    }

    async function handleAddWorkoutBefore() {
        const last = position
         const response = await fetch('/api/workouts', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({program_id: program.id, before: last})
        });

        const workout = await response.json();


         selectedWorkout.set({
            value: workout.data.id, 
            label: workout.data.name || workout.data.automated_name 
        });
        
        selectedProgram.set({
            value: program.id, 
            label: program.name || program.automated_name 
        });

        const currentWorkouts = $workoutsStore;
        const insertIndex = currentWorkouts.findIndex((w: IWorkout) => w.position > workout.data.position);
        
        const index = insertIndex === -1 ? currentWorkouts.length : insertIndex;

      
        
        const updatedWorkouts = [
            ...currentWorkouts.slice(0, index),
            workout.data,
            ...currentWorkouts.slice(index)
        ];


        const temp: any = []

        updatedWorkouts.forEach((w, i) => {
            const code = String.fromCharCode(65+i)
            temp.push({
                ...updatedWorkouts[i],
                position: code,
                automated_name:  `Workout ${code}`
            })
           
        })

         renameProgram();
        workoutsStore.set(temp);

        await fetch(`/api/workouts`, { 
            method: 'PATCH', 
            headers: {
                'Content-Type': 'Application/json'
            }, 
            body: JSON.stringify({ items: temp })
        });

       
        toast.success(workout.message);
       
    }

    async function handleAddWorkoutAfter() {
        const response = await fetch('/api/workouts', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({program_id: program.id, after: position})
        });

        const workout = await response.json();

        selectedWorkout.set({
            value: workout.data.id, 
            label: workout.data.name || workout.data.automated_name 
        });
        
        selectedProgram.set({
            value: program.id, 
            label: program.name || program.automated_name 
        });

        const currentWorkouts = $workoutsStore;
        const insertIndex = currentWorkouts.findIndex((w: IWorkout) => w.position === position);
        
        const updatedWorkouts = [
            ...currentWorkouts.slice(0, insertIndex + 1),
            workout.data,
            ...currentWorkouts.slice(insertIndex + 1)
        ];

        console.log('position', position);
        console.log('added', workout.data.position);

        const temp: any = []

        updatedWorkouts.forEach((w, i) => {
            const code = String.fromCharCode(65+i)
            console.log('code', code);
            temp.push({
                ...updatedWorkouts[i],
                position: code,
                automated_name:  `Workout ${code}`
            })
           
        })

        renameProgram();
        workoutsStore.set(temp);

        await fetch(`/api/workouts`, { 
            method: 'PATCH', 
            headers: {
                'Content-Type': 'Application/json'
            }, 
            body: JSON.stringify({ items: temp })
        });
        
        
        toast.success(workout.message);
    }


    $effect(() => {
        if (!isMobileScreen) {
            isWorkoutOpen = true;
        }
        if(isMobileScreen && isDragging){
            isWorkoutOpen = false;
        } else {
            isWorkoutOpen = true;
        }
    });
      
    function startDrag(e: any) {
		isDragging = false;
	}

    $effect(() => {
        if ($workoutsStore) {
            (async () => {
                exercises = items;
            })();
        }
    });

    onMount( () => {
        exercises = items;
        
        const checkScreenSize = () => {
            const wasDesktop = !isMobileScreen;
            isMobileScreen = window.outerWidth <= 575;
            isTabletScreen = window.outerWidth > 575 && window.outerWidth < 1024;
            
            // Keep workouts expanded when switching from mobile to desktop
            if (wasDesktop !== !isMobileScreen && !isMobileScreen) {
                isWorkoutOpen = true;
            }
        };

        checkScreenSize();
        window.addEventListener('resize', checkScreenSize);

        return () => {
            window.removeEventListener('resize', checkScreenSize);
        };
    });
</script>

<div class="w-full h-full relative border border-slate-100 bg-white shadow-sm flex flex-col">
    <div class="bg-[#313da3] w-full px-3 py-2 flex items-center justify-between border-b flex-shrink-0" style="height: 44px;">
        {#if isEditing}
            <Input
                type="text"
                bind:value={updatedName}
                on:blur={() => { isEditing = false }}
                on:keydown={editWorkoutName}
                class="text-white font-bold w-full py-0 px-1"
            />
        {:else}
            <button 
                class="text-white font-bold text-left overflow-hidden text-ellipsis whitespace-nowrap" 
                onclick={() => { isEditing = true }}
            >
                <!-- {programName }{programName ? "/" : ""}{name || automated_name} -->
                {name || automated_name}
            </button>
        {/if}

        <div class="flex items-center gap-2">
            <DropdownMenu.Root>
                <DropdownMenu.Trigger>
                    <ThreeDotsVertical class=" size-6 text-white" />
                </DropdownMenu.Trigger>
                <DropdownMenu.Content>
                <DropdownMenu.Group class="flex flex-col gap-2 p-2 max-w-[600px] mx-auto sm:top-0">
                    <DropdownMenu.Item class="flex gap-3 bg-[#F0F2FF] rounded justify-start items-center font-semibold text-[16px] text-[#606166]" on:click={() => {
                        handleAddWorkoutBefore();
                    }}>
                        <Arrow90degLeft class="size-5 pointer-events-none text-[#606166]" />
                        <span class="flex-1">Add Workout Before</span>
                    </DropdownMenu.Item>
                    <DropdownMenu.Item class="flex gap-3 bg-[#F0F2FF] rounded justify-start items-center font-semibold text-[16px] text-[#606166]" on:click={() => {
                        handleAddWorkoutAfter();
                    }}>
                        <Arrow90degRight class="size-5 pointer-events-none text-[#606166]" />
                        <span class="flex-1">Add Workout After</span>
                    </DropdownMenu.Item>
                    <DropdownMenu.Item class="flex gap-3 bg-[#F0F2FF] rounded justify-start items-center font-semibold text-[16px] text-[#606166]">
                        <Copy class="size-5 pointer-events-none text-[#606166]" />
                        <span class="flex-1">Copy Workout</span>
                    </DropdownMenu.Item>
                    <!-- <DropdownMenu.Item class="flex gap-4 bg-slate-200 rounded justify-start items-center text-[16px]">
                        <ArrowReturnRight class="size-6 pointer-events-none text-[#606166]" />
                        Duplicate Program
                    </DropdownMenu.Item> -->
                    <DropdownMenu.Item class="flex gap-3 bg-[#F0F2FF] rounded justify-start items-center font-semibold text-[16px] text-[#606166]" on:click={() => {
                        openDeleteWorkoutDialog = true;
                    }}>
                        <Trash class="size-5 pointer-events-none text-[#606166]" />
                        <span class="flex-1">Delete Workout</span>
                    </DropdownMenu.Item>
                  </DropdownMenu.Group>
                </DropdownMenu.Content>
            </DropdownMenu.Root>
        </div>
    </div>
    
    <div 
        bind:this={contentWrapper}
        class="bg-slate-50 transition-all duration-300 ease-in-out h-full overflow-hidden kanban-container"
        class:hidden={!isWorkoutOpen}
        class:block={!isMobileScreen || isWorkoutOpen}
    >
        
        <section 
            class="flex flex-col h-full overflow-y-auto"
            use:dndzone={{items: exercises, flipDurationMs, type: 'workout_items', dragDisabled: isDragging, dropTargetClasses: ["border-2", "border-[#313da3]/[.55]",]}} 
            onconsider={handleDndConsider} 
            onfinalize={handleDndFinalize}
        > 
            {#each exercises as item(item.id)}  
                <div animate:flip={{duration:flipDurationMs}} class="bg-white">
            
                    <div class="">
                        <Exercise item={item} workout_id={id} onClick={() => {
                            selectedExercise = {
                                id: item.data_id,
                                name: item.name,
                                exercise_id: item.exercise_id,
                            }
                            openDeleteExerciseDialog = !openDeleteExerciseDialog
                        }}/>
                    </div>
                </div>    
            {/each}
        </section>
      
    </div>
</div>


<AlertDialog.Root 
    open={openDeleteWorkoutDialog} 
    closeOnEscape 
    closeOnOutsideClick 
    onOpenChange={() => { openDeleteWorkoutDialog = !openDeleteWorkoutDialog }}>
    <AlertDialog.Content>
        <AlertDialog.Header>
            <AlertDialog.Title>Confirm Deletion</AlertDialog.Title>
            <AlertDialog.Description>
                Once deleted, <b>{name || automated_name}</b> cannot be recovered. Proceed?
            </AlertDialog.Description>
        </AlertDialog.Header>
        <AlertDialog.Footer>
            <AlertDialog.Cancel on:click={() => { openDeleteWorkoutDialog = false }}>Cancel</AlertDialog.Cancel>
            <AlertDialog.Action class="bg-red-500" on:click={handleDelete}> Delete </AlertDialog.Action>
        </AlertDialog.Footer>
    </AlertDialog.Content>
</AlertDialog.Root>

<Dialog.Root open={openDeleteExerciseDialog} closeOnEscape closeOnOutsideClick onOpenChange={(val) => { openDeleteExerciseDialog = val }}>
    <Dialog.Content>
      <Dialog.Header>
        <Dialog.Title class="text-center mb-2">Delete Exercise?</Dialog.Title>
        <Dialog.Description class="text-center">
            <p class="mb-2"><b>{selectedExercise.name}</b></p>
            <p>This action cannot be undone.</p>
        </Dialog.Description>
      </Dialog.Header>
      <Dialog.Footer>
        <div class="flex flex-row items-center justify-center gap-2 w-full">
            <Button class="bg-red-500 w-full" on:click={handleDeleteExercise}>Delete</Button>
            <Button variant="outline" class="w-full text-black" on:click={() => { openDeleteExerciseDialog = false }}>Cancel</Button>
        </div>
      </Dialog.Footer>
    </Dialog.Content>
  </Dialog.Root>