<script lang="ts">
	import { Input } from '$lib/components/ui/input/index.js';
	import { Label } from '$lib/components/ui/label/index.js';
	import * as Select from '$lib/components/ui/select';
	import { Button } from '$lib/components/ui/button/index.js';
	import { ArrowClockwise, Disc, Search, XLg } from 'svelte-bootstrap-icons';
	import { Slider } from '$lib/components/ui/slider/index.js';
	import filterOptions from './data.json';
	import { resolveTransformedExercises } from '$lib/utils/tags_exercise.utils';
	import { toast } from 'svelte-sonner';
	import { buildExerciseQuery } from '../Search/helper';
	import { onMount } from 'svelte';
	import { writable } from 'svelte/store';
	import { helpsUrl, filter_is_open } from '$lib/stores/generalStore';


	let {
		searchValue = $bindable(''),
		isFilterOpen = $bindable(false),
		isSearchOpen = $bindable(true),
		resetFilter = $bindable(false),
		selectedUtilities = $bindable(),
		selectedMovement = $bindable(),
		selectedLateral = $bindable(),
		selectedMuscle = $bindable(),
		selectedMechanics = $bindable(),
		selectedForce = $bindable(),
		selectedApparatus = $bindable(),
		tagsData = $bindable(null),
		selectedTag = $bindable(null),
		results = $bindable([]),
		isPremium = $bindable(null)
	} = $props();

	let difficulty = $state([0, 10]);
	let selectedTagName = $state('');
	let searchLoading = $state(false);

	// derived string text for tags arrays
	let filteredTags = $derived(
		tagsData.filter((tag: any) => tag.name.toLowerCase().includes(selectedTagName.toLowerCase()))
	);

	let utilities = $derived(selectedUtilities ? selectedUtilities : 'None');
	let apparatus = $derived(selectedApparatus ? selectedApparatus : 'None');
	let muscle = $derived(selectedMuscle ? selectedMuscle : 'None');

	let isDisabledUtilities = $derived((label: string) => {
		if (
			muscle &&
			!muscle.includes('None') &&
			['Cardio', 'Kettlebell', 'Plyometrics', 'Power', 'Ski', 'Specialized'].includes(label)
		) {
			return true;
		}

		if (
			muscle &&
			!muscle.includes('None') &&
			['Rhomboids', 'Pectoralis Minor', 'Piriformis'].includes(muscle)
		) {
			if (!['None', 'Flexibility'].includes(label)) {
				return true;
			}
		}

		if (muscle.includes('Transverse Abdominis')) {
			if (!['None', 'Auxiliary'].includes(label)) {
				return true;
			}
		}
		return false;
	});

	let isDisabledMuscle = $derived((muscle: string) => {
		if (utilities === 'Auxiliary' || utilities === 'Basic' || utilities === 'Basic or Auxiliary') {
			return ['Rhomboids', 'Pectoralis Minor', 'Piriformis'].includes(muscle);
		}
		return false;
	});

	let isDisabledApparatus = $derived((label: string) => {
		if (utilities && !utilities.includes('None')) {
			if (
				utilities === 'Cardio' &&
				!['Climb', 'Cycle', 'Elliptical', 'Row', 'Ski', 'Step', 'Treadmill'].includes(label)
			) {
				return true;
			}

			if (
				utilities === 'Flexibility' &&
				!['Machine Stretch', 'PNF Stretch', 'Stretch'].includes(label)
			) {
				return true;
			}

			if (utilities === 'Plyometrics' && !['Medicine Ball', 'Bodyweight'].includes(label)) {
				return true;
			}
		}
		if (
			muscle &&
			!muscle.includes('None') &&
			['Climb', 'Cycle', 'Elliptical', 'Row', 'Ski', 'Step', 'Treadmill'].includes(label)
		) {
			return true;
		}

		if (
			muscle &&
			!muscle.includes('None') &&
			['Rhomboids', 'Pectoralis Minor', 'Piriformis'].includes(muscle)
		) {
			if (!['Machine Stretch', 'PNF Stretch', 'Stretch'].includes(label)) {
				return true;
			}
		}
		return false;
	});

	function isGroupDisabled(apparatusGroup: any) {
		return apparatusGroup.every((apparatus: any) => isDisabledApparatus(apparatus.name));
	}

	const getApparatusGroupName = () => {
		if (
			apparatus == 'Cable' ||
			apparatus == 'Lever (plate loaded)' ||
			apparatus == 'Lever (selectorized)' ||
			apparatus == 'Sled' ||
			apparatus == 'Smith' ||
			apparatus == 'Machine-assisted'
		) {
			// Machine
			return 'Machine';
		} else if (
			apparatus == 'Barbell' ||
			apparatus == 'Dumbbell' ||
			apparatus == 'Kettlebell' ||
			apparatus == 'Medicine Ball' ||
			apparatus == 'Rope' ||
			apparatus == 'Atlas Stone' ||
			apparatus == 'Special Barbell' ||
			apparatus == 'Weighted'
		) {
			// Free Weight
			return 'Free Weight';
		} else if (
			apparatus == 'Climb' ||
			apparatus == 'Cycle' ||
			apparatus == 'Elliptical' ||
			apparatus == 'Row' ||
			apparatus == 'Ski' ||
			apparatus == 'Step' ||
			apparatus == 'Treadmill'
		) {
			// Cardio Machine
			return 'Cardio Machine';
		} else if (
			apparatus == 'Band-assisted' ||
			apparatus == 'Isometric' ||
			apparatus == 'Bodyweight' ||
			apparatus == 'Machine-assisted' ||
			apparatus == 'Partner-assisted' ||
			apparatus == 'Self-assisted' ||
			apparatus == 'Jump Rope' ||
			apparatus == 'Suspended'
		) {
			// Bodyweight
			return 'Bodyweight';
		} else if (
			apparatus == 'Machine Stretch' ||
			apparatus == 'PNF Stretch' ||
			apparatus == 'Stretch'
		) {
			// Flexibility
			return 'Flexibility';
		} else {
			return 'null';
		}
	};

	function handleTagSelect(tag: { name: string; id: string }) {
		selectedTag = { name: tag.name, id: tag.id };
	}
	
	function handleInput() {
		// open the filter section when the user starts typing
		isFilterOpen = true;
		filter_is_open.set(true);
		// console.log('Input received:', "Questions");

		helpsUrl.set('https://exrx.net/WorkoutWebApp/SearchFilter');

	}

	function handleKeyDown(event: KeyboardEvent) {
		if (event.key === 'Enter') {
			if (document.activeElement instanceof HTMLElement) {
				document.activeElement.blur();
			}
		}
	}

	let isAnyFilterActive = $derived(
		searchValue !== '' ||
		selectedUtilities !== 'None' ||
		selectedMovement !== 'None' ||
		selectedLateral !== 'None' ||
		selectedMuscle !== 'None' ||
		selectedMechanics !== 'None' ||
		selectedForce !== 'None' ||
		selectedApparatus !== 'None' ||
		selectedTag !== null
	);

	let filterValues = $derived({
		searchValue,
		utilities: selectedUtilities,
		movement: selectedMovement,
		lateral: selectedLateral,
		muscle: selectedMuscle,
		mechanics: selectedMechanics,
		force: selectedForce,
		apparatus: selectedApparatus,
		difficulty,
		apparatusGroup: getApparatusGroupName()
	});

	async function handleSearch() {
		searchLoading = true;
		const link = [selectedUtilities, selectedMovement, selectedLateral, selectedMuscle, selectedMechanics, selectedForce, selectedApparatus].every(
				(v) => v === 'None'
			) && searchValue === ''
				? 'null'
			: buildExerciseQuery(
				searchValue,
				selectedUtilities,
				selectedMovement,
				selectedLateral,
				selectedMuscle,
				selectedMechanics,
				selectedForce,
				selectedApparatus,
				{ low: difficulty[0], high: difficulty[1] },
				getApparatusGroupName()
			);

		if (link === 'null' && selectedTag === null) {
			toast.error('Please provide either tag or a filter to search');
			searchLoading = false;
			return;
		}

		const res = await fetch(
			`/api/filterExercise?link=${encodeURIComponent(link)}&tags=${selectedTag ? selectedTag.id : 'null'}`
		);
		const data = await res.json();
		if (data.success) {
			if (data.data.length === 0) {
				toast.error('No exercises found');
				searchLoading = false;
				return;
			}
			results = [];
			data.data.map((item: any) => {
				results.push({
					id: `${item.Exercise_Id ?? item.id}_temp_${Date.now()}`, 
                    exercise_id: item.Exercise_Id ?? item.id,
					name: item.name ?? item.Exercise_Name_Complete_Abbreviation,
					img: item.img ?? item.Small_Img_1,
					icon: item.icon ?? item.Utility_Icon,
					cat: item.cat ?? item.Overall_Category,
					url: item.url ?? item.URL,
					video_src: item.video_src,
					Larg_Img_1: item.Larg_Img_1,
					invisible:
						typeof item.invisible === 'string'
							? item.invisible === '0'
								? false
								: true
							: (item.invisible ?? false)
				});
			});

			if (!isPremium) {
				results = results.filter((exercise: any) => !exercise.invisible);
			}
			if (results.length === 0) {
				toast.error('No exercises found.');
				searchLoading = false;
				return;
			}
			results = await resolveTransformedExercises(results);
			searchLoading = false;
			// isFilterOpen = false;
			isSearchOpen = false;
			handleReset();
		} else {
			toast.error(data.message);
			searchLoading = false;

		}
	}

	function handleReset() {
		searchValue = '';
		selectedUtilities = 'None';
		selectedMovement = 'None';
		selectedLateral = 'None';
		selectedMuscle = 'None';
		selectedMechanics = 'None';
		selectedForce = 'None';
		selectedApparatus = 'None';
		selectedTag = null;
		difficulty = [0, 10];
		isSearchOpen = true;
	}

	$effect(() => {
		if (resetFilter) {
			resetFilter = false;
			handleReset();
		}
	});
</script>

{#if results.length <= 0 && isSearchOpen }
	<div class="flex flex-row gap-2">
		<div class="relative w-full px-3 py-3">
			<Search class="absolute left-6 top-1/2 -translate-y-1/2 text-black" />
			<Input class="pl-10 border border-black bg-white" placeholder="Search" bind:value={searchValue} on:input={handleInput}
				on:keydown={handleKeyDown}
			/>
		</div>
	</div>
{/if}
{#if isFilterOpen && results.length <= 0}
	<div class="flex flex-col gap-2">
		<div class="flex flex-row items-center bg-[#323DA3] text-white px-4 py-2">
			<div class="flex-1 text-center">
				<Label class="text-lg">Filter</Label>
			</div>
			<button onclick={() => (isFilterOpen = false, filter_is_open.set(false))}>
				<XLg class="size-4" />
			</button>
		</div>
		<!-- Filter Section -->
		<div class="flex flex-row items-center gap-4 px-2">
			<Label class="flex-1 min-w-[60px] font-semibold text-[16px]">Utilities</Label>
			<div class="flex justify-end w-[200px]">
				<Select.Root
					selected={selectedUtilities}
					onSelectedChange={(v) => {
						v && (selectedUtilities = v.value);
					}}
				>
					<Select.Trigger>
						<Select.Value placeholder={utilities} />
					</Select.Trigger>
					<Select.Content>
						{#each filterOptions.filter((option) => option.option_name === 'utilities')[0]['options'] as option}
							<Select.Item value={option?.name} disabled={isDisabledUtilities(option?.name)}
								>{option?.name}</Select.Item
							>
						{/each}
					</Select.Content>
				</Select.Root>
			</div>
		</div>

		{#if utilities && utilities.includes('Plyometrics')}
			<div class="flex flex-row items-center gap-4 px-2">
				<Label class="flex-1 min-w-[60px] font-semibold text-[16px]">Movement Pattern</Label>
				<div class="flex justify-end w-[200px]">
					<Select.Root
						selected={selectedMovement}
						onSelectedChange={(v) => {
							v && (selectedMovement = v.value);
						}}
					>
						<Select.Trigger>
							<Select.Value placeholder="None" />
						</Select.Trigger>
						<Select.Content>
							{#each filterOptions.filter((option) => option.option_name === 'movement')[0]['options'] as option}
								<Select.Item value={option?.name}>{option?.name}</Select.Item>
							{/each}
						</Select.Content>
					</Select.Root>
				</div>
			</div>
		{/if}

		<div class="flex flex-row items-center gap-4 px-2">
			<Label class="flex-1 min-w-[60px] font-semibold text-[16px]">Lateral Pattern</Label>
			<div class="flex justify-end w-[200px]">
				<Select.Root
					selected={selectedLateral}
					onSelectedChange={(v) => {
						v && (selectedLateral = v.value);
					}}
				>
					<Select.Trigger>
						<Select.Value placeholder="None" />
					</Select.Trigger>
					<Select.Content>
						{#each filterOptions.filter((option) => option.option_name === 'lateral')[0]['options'] as option}
							<Select.Item value={option?.name}>{option?.name}</Select.Item>
						{/each}
					</Select.Content>
				</Select.Root>
			</div>
		</div>

		<div
			class="flex flex-row items-center gap-4 px-2 {(utilities &&
				utilities.includes('Plyometrics')) ||
			utilities.includes('Cardio') ||
			utilities.includes('Power') ||
			utilities.includes('Specialized') ||
			utilities.includes('Kettlebell') ||
			apparatus.includes('Climb') ||
			apparatus.includes('Cycle') ||
			apparatus.includes('Elliptical') ||
			apparatus.includes('Row') ||
			apparatus.includes('Ski') ||
			apparatus.includes('Step') ||
			apparatus.includes('Treadmill')
				? 'hidden'
				: ''}"
		>
			<Label class="flex-1 min-w-[60px] font-semibold text-[16px]">Muscle Group</Label>
			<div class="flex justify-end w-[200px]">
				<Select.Root
					selected={selectedMuscle}
					onSelectedChange={(v) => {
						v && (selectedMuscle = v.value);
					}}
				>
					<Select.Trigger>
						<Select.Value placeholder="None" />
					</Select.Trigger>
					<Select.Content class="overflow-y-auto max-h-[300px] lg:max-h-[400px]">
						{#each filterOptions.filter((option) => option.option_name === 'muscle')[0]['options'] as option}
							<Select.Group>
								<Select.Label class="bg-[#323DA3] text-white">{option.group}</Select.Label>
								{#each option.muscles as muscle}
									<Select.Item value={muscle.name} disabled={isDisabledMuscle(muscle.name)}
										>{muscle.name}</Select.Item
									>
								{/each}
							</Select.Group>
						{/each}
					</Select.Content>
				</Select.Root>
			</div>
		</div>

		<div
			class="flex flex-row items-center gap-4 px-2 {(utilities && utilities.includes('Cardio')) ||
			utilities === 'Flexibility' ||
			muscle.includes('Rhomboids') ||
			muscle.includes('Pectoralis Minor') ||
			muscle.includes('Piriformis')
				? 'hidden'
				: ''}"
		>
			<Label class="flex-1 min-w-[60px] font-semibold text-[16px]">Mechanics</Label>
			<div class="flex justify-end w-[200px]">
				<Select.Root
					selected={selectedMechanics}
					onSelectedChange={(v) => {
						v && (selectedMechanics = v.value);
					}}
				>
					<Select.Trigger>
						<Select.Value placeholder="None" />
					</Select.Trigger>
					<Select.Content>
						{#each filterOptions.filter((option) => option.option_name === 'mechanics')[0]['options'] as option}
							<Select.Item value={option?.name}>{option?.name}</Select.Item>
						{/each}
					</Select.Content>
				</Select.Root>
			</div>
		</div>
		<div
			class="flex flex-row items-center gap-4 px-2 {(utilities && utilities.includes('Cardio')) ||
			utilities === 'Flexibility' ||
			muscle.includes('Rhomboids') ||
			muscle.includes('Pectoralis Minor') ||
			muscle.includes('Piriformis')
				? 'hidden'
				: ''}"
		>
			<Label class="flex-1 min-w-[60px] font-semibold text-[16px]">Force</Label>
			<div class="flex justify-end w-[200px]">
				<Select.Root
					selected={selectedForce}
					onSelectedChange={(v) => {
						v && (selectedForce = v.value);
					}}
				>
					<Select.Trigger>
						<Select.Value placeholder="None" />
					</Select.Trigger>
					<Select.Content>
						{#each filterOptions.filter((option) => option.option_name === 'force')[0]['options'] as option}
							<Select.Item value={option?.name}>{option?.name}</Select.Item>
						{/each}
					</Select.Content>
				</Select.Root>
			</div>
		</div>
		<div class="flex flex-row items-center gap-4 px-2">
			<Label class="flex-1 font-semibold text-[16px]">Apparatus</Label>
			<div class="flex justify-end w-[200px]">
				<Select.Root
					selected={selectedApparatus}
					onSelectedChange={(v) => {
						v && (selectedApparatus = v.value);
					}}
				>
					<Select.Trigger>
						<Select.Value placeholder="None" />
					</Select.Trigger>
					<Select.Content class="overflow-y-auto max-h-[300px] lg:max-h-[400px]">
						{#each filterOptions.filter((option) => option.option_name === 'apparatus')[0]['options'] as option}
							<Select.Group>
								<Select.Label
									class={isGroupDisabled(option?.apparatus)
										? 'text-gray-400 cursor-not-allowed'
										: 'bg-[#323DA3] text-white'}>{option?.group}</Select.Label
								>
								{#each option?.apparatus as apparatus}
									<Select.Item
										value={apparatus?.name}
										disabled={isDisabledApparatus(apparatus?.name)}>{apparatus?.name}</Select.Item
									>
								{/each}
							</Select.Group>
						{/each}
					</Select.Content>
				</Select.Root>
			</div>
		</div>

		{#if (apparatus && apparatus.includes('Bodyweight')) || apparatus.includes('Suspended')}
			<div class="flex flex-row items-center gap-4 px-2 mt-2">
				<Label class="flex-1 min-w-[60px] font-semibold">Difficulty</Label>
				<div class="flex justify-end w-[200px]">
					<Slider bind:value={difficulty} max={10} step={1} />
				</div>
			</div>
		{/if}
		<!-- Tag Section -->
		<div class="flex-1 gap-2 text-center bg-[#323DA3] text-white py-2 px-4">
			<Label class="text-lg">Tags</Label>
		</div>
		<div class="mx-2">
			<div class="relative w-full mb-2">
				<Search class="absolute left-3 top-1/2 -translate-y-1/2 text-black" />
				<Input class="pl-10 border border-black bg-white" placeholder="Tag Name" bind:value={selectedTagName} />
			</div>
			<div class="h-[132px] border border-[#A5A6AA] bg-white p-2 rounded-none overflow-y-auto">
				{#if filteredTags}
					{#each filteredTags as tag}
						<button
							class="focus:ring-ring inline-flex select-none items-center border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 rounded-[20px] text-[#423D3D] h-[29px] mx-1"
							style="background-color: {tag.color}"
							onclick={() => handleTagSelect(tag)}
						>
							<span>{tag.name}</span>
						</button>
					{/each}
				{/if}
			</div>
		</div>
		<!-- Button Section -->
		<div class="flex flex-row justify-evenly items-center gap-2">
			<Button
				variant="outline"
				disabled={!isAnyFilterActive || searchLoading}
				class="bg-[#FC570C] w-[194px] h-[51px] text-white rounded-[50px] flex items-center gap-2 px-6 py-3 disabled:bg-[#A5A6AA]"
				on:click={handleReset}
			>
				<ArrowClockwise class="size-5" />
				Reset
			</Button>
			<Button
				variant="outline"
				disabled={!isAnyFilterActive || searchLoading}
				class="bg-[#FC570C] w-[194px] h-[51px] text-white rounded-[50px] flex items-center gap-2 px-6 py-3 disabled:bg-[#A5A6AA]"
				on:click={handleSearch}
			>
				{#if searchLoading}
					<Disc class="mr-2 size-5 animate-spin" />
				{:else}
					<Search class="size-5" />
				{/if}
				Search
			</Button>
		</div>
	</div>
{/if}
