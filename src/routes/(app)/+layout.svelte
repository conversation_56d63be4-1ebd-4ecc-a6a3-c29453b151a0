<script>
	import { <PERSON><PERSON> } from "$lib/components/ui/button";
	import { GearFill, QuestionCircle, Folder, ThreeDotsVertical, Clipboard2Plus, Copy, Globe, Trash, BoxArrowInLeft, Pencil } from "svelte-bootstrap-icons";
    import "../../app.css";
    import { Toaster } from 'svelte-sonner'
    import { goto } from "$app/navigation";
    import { toast } from "svelte-sonner";
    import { get } from "svelte/store";
    import { defaultTab, showDesignPage, layoutAction, showSettingsPage, helpsUrl } from "$lib/stores/generalStore";
    import * as DropdownMenu from '$lib/components/ui/dropdown-menu';
	import { onMount } from "svelte";
    export let data;


    const CONCRETE_URL = data.concreteUrl;
    const WORKOUT_WEB_APP_URL = data.workoutWebAppUrl;
    let isTablet = false;

    function updateSize() {
      isTablet = window.innerWidth >= 768 && window.innerWidth < 1033;
    }

    onMount(() => {	
      updateSize();
      window.addEventListener("resize", updateSize);
      return () => window.removeEventListener("resize", updateSize);
    });
    
    async function handleLogout() {
        try {
            const response = await fetch('/api/auth/logout', {
                method: 'POST'
            });

            if (response.ok) {
                toast.success('Successfully logged out');
                goto('/login');
            } else {
                toast.error('Failed to logout');
            }
        } catch (error) {
            console.error('Logout error:', error);
            toast.error('Failed to logout');
        }
    }

    function sendToHelpSection() {
      const url = get(helpsUrl);

      // Check if the URL is external
      if (url) {
        window.open(url, '_blank'); // Open external URLs in a new tab
      } else {
      window.open('https://exrx.net/WorkoutWebApp', '_blank');
      }
    }
    
    function handleProgramClick () {
        defaultTab.set('programs');
        showDesignPage.set(false);
        showSettingsPage.set(false);
    }
</script>

<svelte:head>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <title>ExRx.net Workout Tools</title>
</svelte:head>

<div class="flex flex-col min-h-[100dvh]">
    <Toaster richColors />
    <div class="hidden md:flex bg-[#313da3] text-white justify-between items-center p-4 py-1">
        <img src="/RunningSilverMan.png" alt="ExRx.net Logo" class="w-[200px] h-16 object-contain" loading="lazy"/>
        <nav>
            <ul class="flex gap-4">
                <li>
                    <a href="#"  onclick={() => { showSettingsPage.set(true)}}>Settings</a>
                </li>
                <li>
                    <button class="hover:underline" onclick={handleLogout}>Logout</button>
                </li>
                {#if isTablet}
                <li>
                    <DropdownMenu.Root>
                        <DropdownMenu.Trigger>
                          <ThreeDotsVertical class="hidden md:block size-6 text-white cursor-pointer" />
                        </DropdownMenu.Trigger>
                      
                        <DropdownMenu.Content class="flex flex-col gap-2 p-4 max-w-[600px] mx-auto sm:top-0">
                          <DropdownMenu.Item
                            class="flex gap-2 bg-[#F0F2FF] rounded justify-start items-center text-[16px]"
                            on:click={() => layoutAction.set('addWorkout')}
                          >
                            <Clipboard2Plus class="size-4 text-gray-600" />
                            Add Workout
                          </DropdownMenu.Item>

                          <DropdownMenu.Item
                            class="flex gap-2 bg-[#F0F2FF] rounded justify-start items-center text-[16px]"
                            on:click={() => layoutAction.set('editWorkout')}
                          >
                            <Pencil class="size-4 text-gray-600" />
                            Toggle Edit Mode
                          </DropdownMenu.Item>
                      
                          <!-- <DropdownMenu.Item
                            class="flex gap-2 bg-[#F0F2FF] rounded justify-start items-center text-[16px]"
                            on:click={() => layoutAction.set('duplicateProgram')}
                          >
                            <Copy class="size-4 text-gray-600" />
                            Duplicate Program
                          </DropdownMenu.Item> -->
                      
                          <DropdownMenu.Item
                            class="flex gap-2 bg-[#F0F2FF] rounded justify-start items-center text-[16px]"
                            on:click={() => layoutAction.set('copyProgramLink')}
                          >
                            <Globe class="size-4 text-gray-600" />
                            Copy Program with Link
                          </DropdownMenu.Item>
                      
                          <DropdownMenu.Item
                            class="flex gap-2 bg-[#F0F2FF] rounded justify-start items-center text-[16px]"
                            on:click={() => layoutAction.set('toggleDeleteMode')}
                          >
                            <Trash class="size-4 text-red-500" />
                            Toggle Delete Mode
                          </DropdownMenu.Item>
                      
                          <DropdownMenu.Item
                            class="flex gap-2 bg-[#F0F2FF] rounded justify-start items-center text-[16px]"
                            on:click={() => layoutAction.set('help')}
                          >
                            <QuestionCircle class="size-4 text-gray-600" />
                            Help
                          </DropdownMenu.Item>
                      
                          <DropdownMenu.Item
                            class="flex gap-2 bg-[#F0F2FF] rounded justify-start items-center text-[16px]"
                            on:click={() => layoutAction.set('logout')}
                          >
                            <BoxArrowInLeft class="size-4 text-gray-600" />
                            Logout
                          </DropdownMenu.Item>
                        </DropdownMenu.Content>
                      </DropdownMenu.Root>
                    
                </li>
                {/if}
            </ul>
        </nav>
    </div>
    <main class="bg-[#F0F2FF] flex-1 overflow-y-auto">
        <slot />
    </main>
    <div class="fixed bottom-0 left-0 right-0 bg-[#323DA3] flex justify-between items-center h-[80px] w-full sm:hidden z-50">
        <Button 
            href="/programs" 
            onclick={handleProgramClick}
            class="flex-1 bg-transparent h-full text-white flex flex-col gap-1 items-center justify-center shadow-none">
            <Folder class="size-6" />
            Programs
        </Button>
        <Button 
            onclick={() => { showSettingsPage.set(true)}}   
            class="flex-1 bg-transparent h-full text-white flex flex-col gap-1 items-center justify-center shadow-none">
            <GearFill class="size-6" />
            Settings
        </Button>
        <Button class="flex-1 bg-transparent h-full text-white flex flex-col gap-1 items-center justify-center shadow-none"
            on:click={ () => {
                sendToHelpSection();
              } 
            }
        >
            <QuestionCircle class="size-6" />
            Help
        </Button>
    </div>
</div>
