import { fail } from '@sveltejs/kit';
import { concreteLogin, directusCustomAuth, login } from '$lib/services/auth.services';
import type { IAuth } from '$lib/typings';
import { getUserStore } from '$lib/stores/store.js';
import { COOKIE_DOMAIN } from '$env/static/private';
import { addUser, getUserByEmail } from '$lib/services/user.services';

export const load = async () => {
  return {
      concreteUrl: import.meta.env.VITE_CONCRETE_URL,
      workoutWebAppUrl: import.meta.env.VITE_WORKOUT_WEB_APP_URL
  };
};

export const actions = {
  default: async ({ request, cookies }) => {
    const formData = await request.formData();
    const email = formData.get('email');
    const password = formData.get('password');

    if (!email || !password) {
      return fail(400, { error: 'Invalid credentials' });
    }

    try {
      const concrete = await concreteLogin({
        email: email,
        password: password
      });


      if (!concrete || !concrete.success) {
        return fail(400, { error: 'Invalid credentials' });
      }

      // Check if the user exist on directus
      let user = await getUserByEmail({email: concrete.user.email});
      
      if (!user) {
        user = await addUser({email: concrete.user.email});
      }     

      const directus = await directusCustomAuth({
        email: email.toString(),
      });
      if (!directus || !directus.access_token) {
        return fail(400, { error: 'Invalid credentials' });
      }


      const authData: IAuth = {
        user: concrete.user,
        isPremium: concrete.isPremium,
        refresh_token: directus.refresh_token,
        access_token: directus.access_token,
        isAuthenticated: true,
      };

      const userStore = getUserStore(concrete.sessionKey);

      userStore.set(authData);

      // cookies.set('CONCRETE5', concrete.sessionKey, {
      //   httpOnly: true,
      //   sameSite: 'strict',
      //   secure: false,  
      //   path: '/',
      //   maxAge: 60 * 60 * 24 * 7, 
      //   domain: COOKIE_DOMAIN
      // });
      const isLocalhost = request.url.includes('localhost');

      cookies.set('CONCRETE5', concrete.sessionKey, {
        httpOnly: true,
        sameSite: isLocalhost ? 'lax' : 'none', // 'Lax' for localhost, 'None' for cross-domain
        secure: !isLocalhost, // Secure required for 'None', disabled for localhost
        path: '/',
        domain: isLocalhost ? 'localhost' : '.exrx.net', // Allow cookie on local + website
        maxAge: 60 * 60 * 24 * 7 // 7 days
      });

      return { success: true, redirect: '/programs' };

    } catch (error) {
      console.error('Login error:', error);
      return fail(500, { error: 'An unexpected error occurred. Please try again.' });
    }
  }
};
